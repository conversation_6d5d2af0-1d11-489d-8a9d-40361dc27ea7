using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Unity.Netcode;
using TMPro;

public class HintGenerator : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI hintText;
    [SerializeField] private PostItNoteNetworkObject parentObject;  // changed to PostItNoteNetworkObject for despawning

    private void Start()
    {
        Debug.LogError("Activating Hint!");
        
        transform.parent.SetParent(null);
        parentObject.Despawn();

        string hint = GenerateHint();
        if (hintText != null)
        {
            hintText.text = hint;
        }
        else
        {
            Debug.LogWarning("Hint Text reference not set on HintGenerator.");
        }
    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.Space))
        {
            Destroy(transform.parent.gameObject);
        }
    }

    string GenerateHint()
    {
        // Get all players in the scene
        var allPlayers = GameObject.FindGameObjectsWithTag("Player")
            .Select(go => go.GetComponent<PlayerSymbol>())
            .Where(ps => ps != null)
            .ToList();

        if (allPlayers.Count == 0)
        {
            return "No players found";
        }

        // Find the local player
        PlayerSymbol localPlayer = allPlayers.FirstOrDefault(p => p.IsOwner);
        var otherPlayers = allPlayers.Where(p => !p.IsOwner).ToList();

        // 10% chance to pick local player, 90% chance to pick another player
        PlayerSymbol targetPlayer;
        if (Random.Range(0f, 1f) < 0.1f || otherPlayers.Count == 0)
        {
            targetPlayer = localPlayer;
        }
        else
        {
            targetPlayer = otherPlayers[Random.Range(0, otherPlayers.Count)];
        }

        if (targetPlayer == null)
        {
            return "Could not find target player";
        }

        // Pick a random symbol to check against
        var allSymbols = System.Enum.GetValues(typeof(PlayerSymbol.Symbol)).Cast<PlayerSymbol.Symbol>().ToArray();
        var randomSymbol = allSymbols[Random.Range(0, allSymbols.Length)];

        // Get the player's actual symbol
        var actualSymbol = targetPlayer.NetworkedSymbol;

        // Get player name
        string playerName = targetPlayer.gameObject.GetComponent<UsernameUpdate>()?.playerName.Value.ToString() ?? "Unknown";

        // Generate hint text
        bool hasSymbol = actualSymbol == randomSymbol;

        if (hasSymbol)
        {
            return $"Player {playerName} has the symbol of {randomSymbol}";
        }
        else
        {
            return $"Player {playerName} does not have the symbol of {randomSymbol}";
        }
    }

    // Update method removed to prevent spacebar conflicts
}

