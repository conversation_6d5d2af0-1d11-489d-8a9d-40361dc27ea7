using System.Collections;
using TMPro;
using UnityEngine;
using Unity.Netcode;

public class RoundAndTime : NetworkBehaviour
{
    public TextMeshPro roundAndTimeText;
    public TextMeshPro periodText;
    
    // Keep public static values for other scripts to reference
    public static int round = 1;
    public static float time = 6.5f * 60f; // Start at 06:30
    
    // Network variables for synchronization
    private NetworkVariable<int> networkRound = new NetworkVariable<int>(1);
    private NetworkVariable<float> networkTime = new NetworkVariable<float>(6.5f * 60f);
    
    private const float realSecondsPerGameDay = 4 * 60; // 3 minutes in real life
    private const float gameSecondsPerGameDay = 24 * 60; // 24 hours in game

    public override void OnNetworkSpawn()
    {
        // Subscribe to network variable changes
        networkRound.OnValueChanged += (int oldValue, int newValue) => round = newValue;
        networkTime.OnValueChanged += (float oldValue, float newValue) => time = newValue;

        if (IsServer)
        {
            StartCoroutine(UpdateTime());
        }
    }

    private IEnumerator UpdateTime()
    {
        while (true)
        {
            yield return new WaitForSeconds(1);

            // Calculate time multiplier based on current hour
            float timeMultiplier = GetTimeMultiplier(networkTime.Value);
            
            // Update network variables with adjusted speed
            networkTime.Value += (gameSecondsPerGameDay / realSecondsPerGameDay) * timeMultiplier;

            if (networkTime.Value >= 24 * 60)
            {
                networkRound.Value++;
                networkTime.Value = 0;
            }

            // Update static values (server-side)
            time = networkTime.Value;
            round = networkRound.Value;

            UpdateDisplay();
        }
    }

    private float GetTimeMultiplier(float currentTimeInMinutes)
    {
        int currentHour = Mathf.FloorToInt(currentTimeInMinutes / 60);
        
        // 5x faster from 0 (24) to 6
        if (currentHour >= 0 && currentHour < 6)
        {
            return 5f;
        }
        // Half speed from 23 to 24 (0)
        else if (currentHour == 23)
        {
            return 0.5f;
        }
        // Half speed during meal times and return to cell
        else if ((currentHour >= 7 && currentHour < 8) ||   // Breakfast
                 (currentHour >= 12 && currentHour < 13) ||  // Lunch
                 (currentHour >= 17 && currentHour < 18))    // Dinner
        {
            return 0.5f;
        }

        else if(currentHour >= 22 && currentHour < 23) // Return to cell
        {
            return 0.3f;
        }
        // Normal speed for all other hours
        else
        {
            return 1f;
        }
    }

    private void Update()
    {
        if (!IsServer)
        {
            UpdateDisplay();
        }
    }

    private void UpdateDisplay()
    {
        // Update the time TextMeshPro field
        int hours = Mathf.FloorToInt(networkTime.Value / 60);
        int minutes = Mathf.FloorToInt(networkTime.Value % 60);
        roundAndTimeText.text = $"Round {networkRound.Value} - {hours:D2}:{minutes:D2}";

        // Update the period TextMeshPro field
        if (hours >= 7 && hours < 8)
        {
            periodText.text = "Breakfast";
        }
        else if (hours >= 12 && hours < 13)
        {
            periodText.text = "Lunch";
        }
        else if (hours >= 17 && hours < 18)
        {
            periodText.text = "Dinner";
        }
        else if (hours >= 22 && hours < 23)
        {
            periodText.text = "Return to Cell";
        }
        else
        {
            periodText.text = "";
        }
    }
}
