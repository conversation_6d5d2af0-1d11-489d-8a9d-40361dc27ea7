using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using Unity.Netcode;

[System.Serializable]
public class LimitedSpawnableObject
{
    [Header("Spawnable Configuration")]
    public GameObject prefab;
    [Range(0f, 100f)]
    public float spawnWeight = 10f;
}

public class LimitedSpawnZone : MonoBehaviour
{
    [Header("Spawnable Objects")]
    [SerializeField] private List<LimitedSpawnableObject> spawnableObjects = new List<LimitedSpawnableObject>();

    [Header("Zone Settings")]
    [SerializeField] private bool visualizeZone = true;
    [SerializeField] private Color zoneColor = Color.blue;

    [Header("Runtime Controls")]
    [SerializeField] private bool debugMode = true;

    private GameObject spawnedObject = null;
    private List<Collider2D> zoneColliders = new List<Collider2D>();
    private bool wasInMealTime = false;
    
    private void Start()
    {
        Debug.LogError($"[LimitedSpawnZone] {gameObject.name} - Start() called");

        // Get all Collider2D components on this GameObject
        Collider2D[] colliders = GetComponents<Collider2D>();
        zoneColliders.Clear();
        zoneColliders.AddRange(colliders);
        Debug.LogError($"[LimitedSpawnZone] {gameObject.name} - Found {colliders.Length} colliders");

        if (zoneColliders.Count == 0)
        {
            Debug.LogError($"[LimitedSpawnZone] {gameObject.name} - ERROR: No colliders found!");
            return;
        }

        if (spawnableObjects.Count == 0)
        {
            Debug.LogError($"[LimitedSpawnZone] {gameObject.name} - ERROR: No spawnable objects configured!");
            return;
        }

        StartCoroutine(WaitForNetworkThenManage());
    }

    private IEnumerator WaitForNetworkThenManage()
    {
        // Wait until NetworkManager exists and is initialized
        while (NetworkManager.Singleton == null || !NetworkManager.Singleton.IsServer)
        {
            yield return null;
        }

        Debug.LogError($"[LimitedSpawnZone] {gameObject.name} - Network initialized, starting meal time management.");
        
        // Start checking meal times
        while (true)
        {
            yield return new WaitForSeconds(1f); // Check every second
            CheckMealTime();
        }
    }

    private void CheckMealTime()
    {
        bool isCurrentlyMealTime = IsMealTime();

        // If we entered meal time and don't have an object spawned
        if (isCurrentlyMealTime && !wasInMealTime && spawnedObject == null)
        {
            SpawnObject();
        }
        // If we left meal time and have an object spawned
        else if (!isCurrentlyMealTime && wasInMealTime && spawnedObject != null)
        {
            DespawnObject();
        }

        wasInMealTime = isCurrentlyMealTime;
    }

    private bool IsMealTime()
    {
        int currentHour = Mathf.FloorToInt(RoundAndTime.time / 60);
        
        return (currentHour >= 7 && currentHour < 8) ||   // Breakfast
               (currentHour >= 12 && currentHour < 13) || // Lunch
               (currentHour >= 17 && currentHour < 18);   // Dinner
    }

    private void SpawnObject()
    {
        if (spawnableObjects.Count == 0) return;

        // Select random spawnable object
        LimitedSpawnableObject selectedObject = SelectWeightedSpawnableObject();
        if (selectedObject == null) return;

        Vector2 spawnPosition = GetRandomPointInZone();
        if (spawnPosition == Vector2.zero) return;

        Debug.LogError($"[LimitedSpawnZone] Spawning '{selectedObject.prefab.name}' for meal time.");

        spawnedObject = Instantiate(selectedObject.prefab, spawnPosition, Quaternion.identity, transform);
        
        NetworkObject netObj = spawnedObject.GetComponent<NetworkObject>();
        if (netObj != null)
        {
            netObj.Spawn();
        }
    }

    private void DespawnObject()
    {
        if (spawnedObject != null)
        {
            Debug.LogError($"[LimitedSpawnZone] Despawning object - meal time ended.");
            
            NetworkObject netObj = spawnedObject.GetComponent<NetworkObject>();
            if (netObj != null && netObj.IsSpawned)
            {
                netObj.Despawn(true);
            }
            else
            {
                DestroyImmediate(spawnedObject);
            }
            
            spawnedObject = null;
        }
    }

    private LimitedSpawnableObject SelectWeightedSpawnableObject()
    {
        if (spawnableObjects.Count == 0) return null;
        
        // Weighted random selection
        float totalWeight = 0f;
        foreach (var obj in spawnableObjects)
            totalWeight += obj.spawnWeight;
        
        float randomValue = Random.Range(0f, totalWeight);
        float currentWeight = 0f;
        
        foreach (var obj in spawnableObjects)
        {
            currentWeight += obj.spawnWeight;
            if (randomValue <= currentWeight)
                return obj;
        }
        
        return spawnableObjects[spawnableObjects.Count - 1]; // Fallback
    }

    private Vector2 GetRandomPointInZone()
    {
        if (zoneColliders.Count == 0) return Vector2.zero;
        
        // Calculate total area of all colliders for weighted selection
        List<float> colliderAreas = new List<float>();
        float totalArea = 0f;
        
        foreach (Collider2D collider in zoneColliders)
        {
            float area = collider.bounds.size.x * collider.bounds.size.y;
            colliderAreas.Add(area);
            totalArea += area;
        }
        
        // Select a collider based on area weighting
        float randomValue = Random.Range(0f, totalArea);
        float currentWeight = 0f;
        int selectedColliderIndex = 0;
        
        for (int i = 0; i < colliderAreas.Count; i++)
        {
            currentWeight += colliderAreas[i];
            if (randomValue <= currentWeight)
            {
                selectedColliderIndex = i;
                break;
            }
        }
        
        // Get random point within the selected collider
        Collider2D selectedCollider = zoneColliders[selectedColliderIndex];
        Bounds bounds = selectedCollider.bounds;
        
        int maxAttempts = 100;
        for (int i = 0; i < maxAttempts; i++)
        {
            Vector2 randomPoint = new Vector2(
                Random.Range(bounds.min.x, bounds.max.x),
                Random.Range(bounds.min.y, bounds.max.y)
            );
            
            if (selectedCollider.OverlapPoint(randomPoint))
                return randomPoint;
        }
        
        // Fallback to collider center
        return selectedCollider.bounds.center;
    }

    private void OnDrawGizmos()
    {
        if (!visualizeZone) return;
        
        foreach (Collider2D col in zoneColliders)
        {
            if (col != null)
            {
                Gizmos.color = new Color(zoneColor.r, zoneColor.g, zoneColor.b, 0.3f);
                Gizmos.DrawCube(col.bounds.center, col.bounds.size);
                
                Gizmos.color = zoneColor;
                Gizmos.DrawWireCube(col.bounds.center, col.bounds.size);
            }
        }
    }
}