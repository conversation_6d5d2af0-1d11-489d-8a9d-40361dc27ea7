#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(SpawnZone))]
public class SpawnZoneEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        SpawnZone spawnZone = (SpawnZone)target;
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Zone Controls", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Spawn Objects Now"))
        {
            spawnZone.SpawnObjects();
        }
        
        if (GUILayout.<PERSON><PERSON>("Clear Spawned Objects"))
        {
            spawnZone.ClearSpawnedObjects();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox(
            "1. Add a Collider2D to define the spawn zone\n" +
            "2. Configure spawnable objects with their spawn chances\n" +
            "3. Set min/max objects to spawn\n" +
            "4. Use context menu or buttons to test spawning", 
            MessageType.Info);
    }
}
#endif