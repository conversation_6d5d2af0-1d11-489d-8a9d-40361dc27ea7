using System.Collections.Generic;
using UnityEngine;

namespace LobbyRelaySample.UI
{
    public class LobbyUserListUI : UIPanelBase
    {
        [SerializeField]
        List<InLobbyUserUI> m_UserUIObjects = new List<InLobbyUserUI>();

        [SerializeField]
        [Tooltip("Prefab to instantiate for additional player slots beyond the initial ones")]
        InLobbyUserUI m_UserUIPrefab;

        [SerializeField]
        [Tooltip("Parent transform where additional player UI elements will be instantiated")]
        Transform m_UserUIParent;

        LocalLobby m_LocalLobby;
        List<InLobbyUserUI> m_DynamicallyCreatedUIObjects = new List<InLobbyUserUI>();

        public override void Start()
        {
            base.Start();
            m_LocalLobby = GameManager.Instance.LocalLobby;
            m_LocalLobby.onUserJoined += OnUserJoined;
            m_LocalLobby.onUserLeft += OnUserLeft;

            // If no parent is set, use this transform as the parent
            if (m_UserUIParent == null)
                m_UserUIParent = transform;
        }

        void OnUserJoined(LocalPlayer localPlayer)
        {
            SynchPlayerUI();
        }

        void OnUserLeft(int i)
        {
            SynchPlayerUI();
        }

        void SynchPlayerUI()
        {
            // Reset all existing UI objects
            foreach (var ui in m_UserUIObjects)
                ui.ResetUI();
            foreach (var ui in m_DynamicallyCreatedUIObjects)
                ui.ResetUI();

            // Ensure we have enough UI objects for all players
            EnsureEnoughUIObjects();

            // Update UI for all players
            for (int i = 0; i < m_LocalLobby.PlayerCount; i++)
            {
                var lobbySlot = GetUIObjectAtIndex(i);
                var player = m_LocalLobby.GetLocalPlayer(i);
                if (player == null)
                    continue;
                lobbySlot.SetUser(player);
            }
        }

        void EnsureEnoughUIObjects()
        {
            int totalNeeded = m_LocalLobby.PlayerCount;
            int totalAvailable = m_UserUIObjects.Count + m_DynamicallyCreatedUIObjects.Count;

            if (totalNeeded > totalAvailable)
            {
                int additionalNeeded = totalNeeded - totalAvailable;
                for (int i = 0; i < additionalNeeded; i++)
                {
                    CreateAdditionalUIObject();
                }
            }
        }

        void CreateAdditionalUIObject()
        {
            if (m_UserUIPrefab == null)
            {
                Debug.LogError("LobbyUserListUI: Cannot create additional UI objects - m_UserUIPrefab is not assigned!");
                return;
            }

            var newUIObject = Instantiate(m_UserUIPrefab, m_UserUIParent);
            m_DynamicallyCreatedUIObjects.Add(newUIObject);
        }

        InLobbyUserUI GetUIObjectAtIndex(int index)
        {
            if (index < m_UserUIObjects.Count)
            {
                return m_UserUIObjects[index];
            }
            else
            {
                int dynamicIndex = index - m_UserUIObjects.Count;
                return m_DynamicallyCreatedUIObjects[dynamicIndex];
            }
        }

        void OnDestroy()
        {
            // Clean up dynamically created objects
            foreach (var ui in m_DynamicallyCreatedUIObjects)
            {
                if (ui != null)
                    DestroyImmediate(ui.gameObject);
            }
            m_DynamicallyCreatedUIObjects.Clear();
        }
    }
}