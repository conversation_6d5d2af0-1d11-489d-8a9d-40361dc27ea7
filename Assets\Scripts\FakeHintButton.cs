using UnityEngine;
using TMPro;
using Unity.Netcode;

public class FakeHintButton : NetworkBehaviour
{
    [SerializeField] private TextMeshProUGUI buttonText;
    [SerializeField] private GameObject fakeHintPrefab;

    // Make this local instead of networked
    private int localCount = 1;
    private int currentRound = -1; // Track the current round

    private void Start()
    {
        if (buttonText == null)
        {
            Debug.LogWarning("Button Text not assigned.");
        }

        UpdateButtonText();
    }

    private void Update()
    {
        // Check if round has changed and reset counter every 2 rounds
        if (RoundAndTime.round != currentRound)
        {
            currentRound = RoundAndTime.round;
            
            // Reset every 2 rounds (rounds 1, 3, 5, etc.)
            if (currentRound % 2 == 1)
            {
                ResetCount();
            }
        }
    }

    public void OnButtonClick()
    {
        if (localCount == 1)
        {
            var localPlayer = FindLocalPlayer();
            if (localPlayer != null)
            {
                PlaceFakeHintServerRpc(localPlayer.transform.position);
                localCount = 0;
                UpdateButtonText();
            }
            else
            {
                Debug.LogWarning("Local player not found.");
            }
        }
        else
        {
            Debug.Log("Fake hint unavailable. Can't place.");
        }
    }

    private void ResetCount()
    {
        localCount = 1;
        UpdateButtonText();
    }

    private GameObject FindLocalPlayer()
    {
        var players = GameObject.FindGameObjectsWithTag("Player");
        foreach (var player in players)
        {
            var netObj = player.GetComponent<NetworkObject>();
            if (netObj != null && netObj.IsOwner)
            {
                Debug.LogError("[FakeHintButton] Found local player: " + player.name);
                return player;
            }
        }
        Debug.LogError("[FakeHintButton] Local player not found.");
        return null;
    }

    private void UpdateButtonText()
    {
        if (buttonText == null) return;

        buttonText.text = localCount == 1
            ? "Place Fake Hint (available)"
            : "Place Fake Hint (unavailable)";
    }

    [ServerRpc(RequireOwnership = false)]  // Allow any client to call this
    private void PlaceFakeHintServerRpc(Vector3 position)
    {
        var fakeHintInstance = Instantiate(fakeHintPrefab, position, Quaternion.identity);
        fakeHintInstance.GetComponent<NetworkObject>().Spawn();
    }
}



