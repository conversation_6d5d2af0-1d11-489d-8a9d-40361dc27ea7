﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Unity.Netcode;
using Unity.Netcode.Transports.UTP;
using Unity.Networking.Transport;
using Unity.Services.Authentication;
using Unity.Services.Relay;
using Unity.Services.Relay.Models;
using UnityEngine;

namespace LobbyRelaySample.ngo
{
    /// <summary>
    /// Once the local localPlayer is in a localLobby and that localLobby has entered the In-Game state, this will load in whatever is necessary to actually run the game part.
    /// This will exist in the game scene so that it can hold references to scene objects that spawned prefab instances will need.
    /// </summary>
    public class SetupInGame : MonoBehaviour
    {
        [SerializeField]
        GameObject m_IngameRunnerPrefab = default;
        [SerializeField]
        private GameObject[] m_disableWhileInGame = default;

        private InGameRunner m_inGameRunner;

        private bool m_doesNeedCleanup = false;
        private bool m_hasConnectedViaNGO = false;

        private LocalLobby m_lobby;

        private void Awake()
        {
            DontDestroyOnLoad(transform.root.gameObject);
        }

        private void SetMenuVisibility(bool areVisible)
        {
            foreach (GameObject go in m_disableWhileInGame)
                go.SetActive(areVisible);
        }

        /// <summary>
        /// The prefab with the NetworkManager contains all of the assets and logic needed to set up the NGO minigame.
        /// The UnityTransport needs to also be set up with a new Allocation from Relay.
        /// </summary>
        async Task CreateNetworkManager(LocalLobby localLobby, LocalPlayer localPlayer)
        {
            Debug.LogError($"[Relay] CreateNetworkManager called - IsHost: {localPlayer.IsHost.Value}");
            
            m_lobby = localLobby;
            m_inGameRunner = Instantiate(m_IngameRunnerPrefab).GetComponentInChildren<InGameRunner>();
            m_inGameRunner.Initialize(OnConnectionVerified, m_lobby.PlayerCount, OnGameBegin, OnGameEnd, localPlayer);
            
            if (localPlayer.IsHost.Value)
            {
                Debug.LogError("[Relay] Host - Setting up Relay host data");
                await SetRelayHostData();
                Debug.LogError("[Relay] Host - Starting NetworkManager as host");
                NetworkManager.Singleton.StartHost();
            }
            else
            {
                Debug.LogError("[Relay] Client - Awaiting relay code");
                await AwaitRelayCode(localLobby);
                Debug.LogError("[Relay] Client - Setting up Relay client data");
                await SetRelayClientData();
                Debug.LogError("[Relay] Client - Starting NetworkManager as client");
                NetworkManager.Singleton.StartClient();
            }
        }

        async Task AwaitRelayCode(LocalLobby lobby)
        {
            string relayCode = lobby.RelayCode.Value;
            lobby.RelayCode.onChanged += (code) => relayCode = code;
            while (string.IsNullOrEmpty(relayCode))
            {
                await Task.Delay(100);
            }
        }

        async Task SetRelayHostData()
        {
            Debug.LogError($"[Relay] SetRelayHostData - Creating allocation for {m_lobby.MaxPlayerCount.Value} players");
            
            // Check authentication first
            if (!AuthenticationService.Instance.IsSignedIn)
            {
                Debug.LogError("[Relay] ERROR: Not authenticated! Cannot create Relay allocation.");
                return;
            }
            
            Debug.LogError($"[Relay] Authentication confirmed - Player ID: {AuthenticationService.Instance.PlayerId}");
            
            UnityTransport transport = NetworkManager.Singleton.GetComponentInChildren<UnityTransport>();
            
            try
            {
                Debug.LogError("[Relay] Calling CreateAllocationAsync...");
                var allocation = await Relay.Instance.CreateAllocationAsync(m_lobby.MaxPlayerCount.Value);
                Debug.LogError($"[Relay] Allocation created successfully - AllocationId: {allocation.AllocationId}");
                
                Debug.LogError("[Relay] Calling GetJoinCodeAsync...");
                var joincode = await Relay.Instance.GetJoinCodeAsync(allocation.AllocationId);
                Debug.LogError($"[Relay] Join code generated: {joincode}");
                
                GameManager.Instance.HostSetRelayCode(joincode);

                bool isSecure = false;
                var endpoint = GetEndpointForAllocation(allocation.ServerEndpoints,
                    allocation.RelayServer.IpV4, allocation.RelayServer.Port, out isSecure);

                Debug.LogError($"[Relay] Setting host relay data - Endpoint: {endpoint}, Secure: {isSecure}");
                
                transport.SetHostRelayData(AddressFromEndpoint(endpoint), endpoint.Port,
                    allocation.AllocationIdBytes, allocation.Key, allocation.ConnectionData, isSecure);
                    
                Debug.LogError("[Relay] Host relay data set successfully");
            }
            catch (Unity.Services.Relay.RelayServiceException e)
            {
                Debug.LogError($"[Relay] RelayServiceException: {e.Reason} - {e.Message}");
                Debug.LogError($"[Relay] Error Code: {e.ErrorCode}");
                
                // Check for specific quota/billing errors
                if (e.Reason == Unity.Services.Relay.RelayExceptionReason.RateLimited)
                {
                    Debug.LogError("[Relay] RATE LIMITED - You've exceeded the free tier limits!");
                }
                else if (e.Reason == Unity.Services.Relay.RelayExceptionReason.Forbidden)
                {
                    Debug.LogError("[Relay] FORBIDDEN - Service not enabled or billing issue!");
                }
                
                throw;
            }
            catch (Unity.Services.Core.RequestFailedException e)
            {
                Debug.LogError($"[Relay] RequestFailedException: {e.ErrorCode} - {e.Message}");
                
                // Check for HTTP status codes
                if (e.ErrorCode == 429)
                {
                    Debug.LogError("[Relay] HTTP 429 - Rate limit exceeded!");
                }
                else if (e.ErrorCode == 403)
                {
                    Debug.LogError("[Relay] HTTP 403 - Forbidden, check service configuration!");
                }
                else if (e.ErrorCode == 402)
                {
                    Debug.LogError("[Relay] HTTP 402 - Payment required, billing issue!");
                }
                
                throw;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[Relay] General Exception: {e.GetType().Name} - {e.Message}");
                
                // Log inner exceptions which might contain the actual HTTP response
                var innerEx = e.InnerException;
                while (innerEx != null)
                {
                    Debug.LogError($"[Relay] Inner Exception: {innerEx.GetType().Name} - {innerEx.Message}");
                    innerEx = innerEx.InnerException;
                }
                
                // Check if it's a ResponseDeserializationException specifically
                if (e.GetType().Name.Contains("ResponseDeserialization"))
                {
                    Debug.LogError("[Relay] This is a JSON parsing error - the server returned an unexpected response format");
                    Debug.LogError("[Relay] This usually means:");
                    Debug.LogError("[Relay] 1. Service not properly enabled in Unity Dashboard");
                    Debug.LogError("[Relay] 2. Billing/quota issues");
                    Debug.LogError("[Relay] 3. Project ID mismatch");
                    Debug.LogError("[Relay] 4. Service temporarily unavailable");
                }
                
                Debug.LogError($"[Relay] Stack Trace: {e.StackTrace}");
                throw;
            }
        }

        async Task SetRelayClientData()
        {
            Debug.LogError($"[Relay] SetRelayClientData - Joining with code: {m_lobby.RelayCode.Value}");
            
            UnityTransport transport = NetworkManager.Singleton.GetComponentInChildren<UnityTransport>();

            try
            {
                var joinAllocation = await Relay.Instance.JoinAllocationAsync(m_lobby.RelayCode.Value);
                Debug.LogError("[Relay] Join allocation successful");
                
                bool isSecure = false;
                var endpoint = GetEndpointForAllocation(joinAllocation.ServerEndpoints,
                    joinAllocation.RelayServer.IpV4, joinAllocation.RelayServer.Port, out isSecure);

                Debug.LogError($"[Relay] Setting client relay data - Endpoint: {endpoint}, Secure: {isSecure}");
                
                transport.SetClientRelayData(AddressFromEndpoint(endpoint), endpoint.Port,
                    joinAllocation.AllocationIdBytes, joinAllocation.Key,
                    joinAllocation.ConnectionData, joinAllocation.HostConnectionData, isSecure);
                    
                Debug.LogError("[Relay] Client relay data set successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[Relay] ERROR in SetRelayClientData: {e.Message}");
                throw;
            }
        }

        /// <summary>
        /// Determine the server endpoint for connecting to the Relay server, for either an Allocation or a JoinAllocation.
        /// If DTLS encryption is available, and there's a secure server endpoint available, use that as a secure connection. Otherwise, just connect to the Relay IP unsecured.
        /// </summary>
        NetworkEndPoint GetEndpointForAllocation(
            List<RelayServerEndpoint> endpoints,
            string ip,
            int port,
            out bool isSecure)
        {
#if ENABLE_MANAGED_UNITYTLS
            foreach (RelayServerEndpoint endpoint in endpoints)
            {
                if (endpoint.Secure && endpoint.Network == RelayServerEndpoint.NetworkOptions.Udp)
                {
                    isSecure = true;
                    return NetworkEndPoint.Parse(endpoint.Host, (ushort)endpoint.Port);
                }
            }
#endif
            isSecure = false;
            return NetworkEndPoint.Parse(ip, (ushort)port);
        }

        string AddressFromEndpoint(NetworkEndPoint endpoint)
        {
            return endpoint.Address.Split(':')[0];
        }

        void OnConnectionVerified()
        {
            m_hasConnectedViaNGO = true;
        }

        public void StartNetworkedGame(LocalLobby localLobby, LocalPlayer localPlayer)
        {
            m_doesNeedCleanup = true;
            SetMenuVisibility(false);
#pragma warning disable 4014
            CreateNetworkManager(localLobby, localPlayer);
#pragma warning restore 4014
        }

        public void OnGameBegin()
        {
            if (!m_hasConnectedViaNGO)
            {
                // If this localPlayer hasn't successfully connected via NGO, forcibly exit the minigame.
                LogHandlerSettings.Instance.SpawnErrorPopup("Failed to join the game.");
                OnGameEnd();
            }
        }

        /// <summary>
        /// Return to the localLobby after the game, whether due to the game ending or due to a failed connection.
        /// </summary>
        public void OnGameEnd()
        {
            if (m_doesNeedCleanup)
            {
                NetworkManager.Singleton.Shutdown(true);
                Destroy(m_inGameRunner
                    .transform.parent
                    .gameObject); // Since this destroys the NetworkManager, that will kick off cleaning up networked objects.
                SetMenuVisibility(true);
                m_lobby.RelayCode.Value = "";
                GameManager.Instance.EndGame();
                m_doesNeedCleanup = false;
            }
        }
    }
}








