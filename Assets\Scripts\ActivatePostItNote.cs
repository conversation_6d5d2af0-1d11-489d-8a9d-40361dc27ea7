using UnityEngine;
using Unity.Netcode;

public class ActivatePostItNote : NetworkBehaviour
{
    [SerializeField] private GameObject postItNote;

    private bool isPlayerInside = false;

    private void Update()
    {
        if (isPlayerInside && Input.GetKeyDown(KeyCode.Space))
        {
            if (postItNote != null)
            {
                postItNote.SetActive(true);
            }
            else
            {
                Debug.Log<PERSON>arning("No GameObject assigned to enable.");
            }
        }
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            isPlayerInside = true;
        }
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            isPlayerInside = false;
        }
    }
}