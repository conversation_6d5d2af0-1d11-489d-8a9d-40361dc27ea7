using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using Unity.Netcode;  // Make sure to add this for network functionality

[System.Serializable]
public class SpawnableObject
{
    [Header("Spawnable Configuration")]
    public GameObject prefab;
    [Range(0f, 100f)]
    public float spawnWeight = 10f;  // Changed from spawnChance to weight
    [Range(1, 10)]
    public int maxInstancesPerZone = 1;
    
    [Header("Positioning")]
    public bool canOverlap = false;
    public float minDistanceFromOthers = 1f;
}

public class SpawnZone : MonoBehaviour
{
    [Header("Zone Configuration")]
    [SerializeField] private int minObjectsToSpawn = 1;
    [SerializeField] private int maxObjectsToSpawn = 3;

    [Header("Spawnable Objects")]
    [SerializeField] private List<SpawnableObject> spawnableObjects = new List<SpawnableObject>();

    [Header("Zone Settings")]
    [SerializeField] private bool spawnOnStart = true;
    [SerializeField] private bool visualizeZone = true;
    [SerializeField] private Color zoneColor = Color.green;
    [SerializeField] private LayerMask obstacleLayerMask = 0; // Changed to 0 (nothing blocks)

    [Header("Spawn Timing")]
    [SerializeField] private float spawnDelay = 0f;
    [SerializeField] private bool canRespawn = false;
    [SerializeField] private float respawnInterval = 30f;

    [Header("Runtime Controls")]
    [SerializeField] private bool enableRuntimeSpawning = true;
    [SerializeField] private bool debugMode = true; // Enable by default for troubleshooting

    private List<GameObject> spawnedObjects = new List<GameObject>();
    private List<Collider2D> zoneColliders = new List<Collider2D>();
    private bool hasSpawned = false;
    
    private void Start()
    {
        Debug.LogError($"[SpawnZone] {gameObject.name} - Start() called");

        // Get all Collider2D components on this GameObject
        Collider2D[] colliders = GetComponents<Collider2D>();
        zoneColliders.Clear();
        zoneColliders.AddRange(colliders);
        Debug.LogError($"[SpawnZone] {gameObject.name} - Found {colliders.Length} colliders");

        if (zoneColliders.Count == 0)
        {
            Debug.LogError($"[SpawnZone] {gameObject.name} - ERROR: No colliders found!");
            return;
        }

        if (spawnableObjects.Count == 0)
        {
            Debug.LogError($"[SpawnZone] {gameObject.name} - ERROR: No spawnable objects configured!");
            return;
        }

        if (spawnOnStart)
        {
            Debug.LogError($"[SpawnZone] {gameObject.name} - Waiting for NetworkManager to initialize...");
            StartCoroutine(WaitForNetworkThenSpawn());
        }

        if (canRespawn)
            StartCoroutine(RespawnRoutine());
    }

    private IEnumerator WaitForNetworkThenSpawn()
    {
        // Wait until NetworkManager exists and is initialized
        while (NetworkManager.Singleton == null || !NetworkManager.Singleton.IsServer)
        {
            yield return null; // Wait one frame
        }

        if (spawnDelay > 0f)
        {
            Debug.LogError($"[SpawnZone] {gameObject.name} - Delaying spawn by {spawnDelay} seconds");
            yield return new WaitForSeconds(spawnDelay);
        }

        Debug.LogError($"[SpawnZone] {gameObject.name} - Network initialized, spawning now.");
        SpawnObjects();
    }
    
    private IEnumerator DelayedSpawn()
    {
        yield return new WaitForSeconds(spawnDelay);
        SpawnObjects();
    }
    
    private IEnumerator RespawnRoutine()
    {
        while (canRespawn)
        {
            yield return new WaitForSeconds(respawnInterval);
            
            // Check if any spawned objects are missing and replace them
            if (hasSpawned)
            {
                CheckAndReplaceDestroyedObjects();
            }
        }
    }
    
    private void CheckAndReplaceDestroyedObjects()
    {
        if (!NetworkManager.Singleton.IsServer)
            return;
            
        // Count how many objects are missing
        int missingCount = 0;
        for (int i = spawnedObjects.Count - 1; i >= 0; i--)
        {
            if (spawnedObjects[i] == null)
            {
                spawnedObjects.RemoveAt(i);
                missingCount++;
            }
        }
        
        if (missingCount > 0)
        {
            Debug.LogError($"[SpawnZone] Found {missingCount} missing objects, spawning replacements.");
            
            // Spawn replacements
            Dictionary<GameObject, int> spawnCounts = new Dictionary<GameObject, int>();
            List<Vector2> usedPositions = new List<Vector2>();
            
            // Count existing objects
            foreach (GameObject obj in spawnedObjects)
            {
                if (obj != null)
                {
                    SpawnableObject matchingSpawnable = spawnableObjects.Find(s => s.prefab.name == obj.name.Replace("(Clone)", ""));
                    if (matchingSpawnable != null)
                    {
                        if (!spawnCounts.ContainsKey(matchingSpawnable.prefab))
                            spawnCounts[matchingSpawnable.prefab] = 0;
                        spawnCounts[matchingSpawnable.prefab]++;
                    }
                    
                    usedPositions.Add(obj.transform.position);
                }
            }
            
            // Spawn missing objects
            for (int i = 0; i < missingCount; i++)
            {
                SpawnableObject selectedObject = SelectWeightedSpawnableObject(spawnCounts);
                if (selectedObject == null) continue;
                
                Vector2 spawnPosition = GetValidSpawnPosition(usedPositions, selectedObject);
                if (spawnPosition == Vector2.zero) continue;
                
                GameObject spawned = Instantiate(selectedObject.prefab, spawnPosition, Quaternion.identity, transform);
                if (spawned == null) continue;
                
                NetworkObject netObj = spawned.GetComponent<NetworkObject>();
                if (netObj != null)
                {
                    netObj.Spawn();
                }
                
                spawnedObjects.Add(spawned);
                
                if (!selectedObject.canOverlap)
                    usedPositions.Add(spawnPosition);
                    
                if (!spawnCounts.ContainsKey(selectedObject.prefab))
                    spawnCounts[selectedObject.prefab] = 0;
                spawnCounts[selectedObject.prefab]++;
                
                Debug.LogError($"[SpawnZone] Respawned '{selectedObject.prefab.name}' to replace destroyed object.");
            }
        }
    }
    
    [ContextMenu("Spawn Objects")]
    public void SpawnObjects()
    {
        if (!enableRuntimeSpawning)
        {
            Debug.LogError("[SpawnZone] Runtime spawning disabled. Aborting spawn.");
            return;
        }

        if (!NetworkManager.Singleton.IsServer)
        {
            Debug.LogError("[SpawnZone] SpawnObjects called on client. Only server can spawn. Aborting spawn.");
            return;
        }

        if (spawnableObjects.Count == 0)
        {
            Debug.LogError("[SpawnZone] No spawnable objects configured! Add prefabs to spawnableObjects list.");
            return;
        }

        if (zoneColliders.Count == 0)
        {
            Debug.LogError("[SpawnZone] No zone colliders found! Make sure the zone has Collider2D components.");
            return;
        }

        Debug.LogError($"[SpawnZone] Starting spawn. Min: {minObjectsToSpawn}, Max: {maxObjectsToSpawn}, Total spawnables: {spawnableObjects.Count}");

        ClearSpawnedObjects();

        int objectsToSpawn = Random.Range(minObjectsToSpawn, maxObjectsToSpawn + 1);
        Debug.LogError($"[SpawnZone] Will attempt to spawn {objectsToSpawn} objects.");

        List<Vector2> usedPositions = new List<Vector2>();
        Dictionary<GameObject, int> spawnCounts = new Dictionary<GameObject, int>();

        for (int i = 0; i < objectsToSpawn; i++)
        {
            SpawnableObject selectedObject = SelectWeightedSpawnableObject(spawnCounts);
            if (selectedObject == null)
            {
                Debug.LogError($"[SpawnZone] No available spawnable object found at iteration {i}. Maybe max instances reached?");
                continue;
            }
            else
            {
                Debug.LogError($"[SpawnZone] Selected prefab '{selectedObject.prefab.name}' for spawn.");
            }

            Vector2 spawnPosition = GetValidSpawnPosition(usedPositions, selectedObject);
            if (spawnPosition == Vector2.zero)
            {
                Debug.LogError($"[SpawnZone] Failed to find a valid spawn position for '{selectedObject.prefab.name}' at iteration {i}.");
                continue;
            }

            Debug.LogError($"[SpawnZone] Spawning '{selectedObject.prefab.name}' at position {spawnPosition}.");

            GameObject spawned = Instantiate(selectedObject.prefab, spawnPosition, Quaternion.identity, transform);
            if (spawned == null)
            {
                Debug.LogError($"[SpawnZone] Instantiate failed for '{selectedObject.prefab.name}'!");
                continue;
            }

            NetworkObject netObj = spawned.GetComponent<NetworkObject>();
            if (netObj != null)
            {
                Debug.LogError($"[SpawnZone] Found NetworkObject on '{selectedObject.prefab.name}', calling Spawn().");
                netObj.Spawn();
            }
            else
            {
                Debug.LogError($"[SpawnZone] No NetworkObject component found on '{selectedObject.prefab.name}'. Spawn will not be networked.");
            }

            spawnedObjects.Add(spawned);

            if (!selectedObject.canOverlap)
                usedPositions.Add(spawnPosition);

            if (!spawnCounts.ContainsKey(selectedObject.prefab))
                spawnCounts[selectedObject.prefab] = 0;
            spawnCounts[selectedObject.prefab]++;

            Debug.LogError($"[SpawnZone] Successfully spawned '{selectedObject.prefab.name}'. Current count for this prefab: {spawnCounts[selectedObject.prefab]}");
        }

        hasSpawned = true;
        Debug.LogError("[SpawnZone] Spawning completed.");
    }
    
    private SpawnableObject SelectWeightedSpawnableObject(Dictionary<GameObject, int> spawnCounts)
    {
        List<SpawnableObject> availableObjects = new List<SpawnableObject>();
        List<float> weights = new List<float>();
        
        foreach (var spawnableObj in spawnableObjects)
        {
            // Check if we haven't exceeded max instances
            int currentCount = spawnCounts.ContainsKey(spawnableObj.prefab) ? spawnCounts[spawnableObj.prefab] : 0;
            if (currentCount < spawnableObj.maxInstancesPerZone)
            {
                availableObjects.Add(spawnableObj);
                weights.Add(spawnableObj.spawnWeight);
            }
        }
        
        if (availableObjects.Count == 0) return null;
        
        // Weighted random selection
        float totalWeight = 0f;
        foreach (float weight in weights)
            totalWeight += weight;
        
        float randomValue = Random.Range(0f, totalWeight);
        float currentWeight = 0f;
        
        for (int i = 0; i < availableObjects.Count; i++)
        {
            currentWeight += weights[i];
            if (randomValue <= currentWeight)
                return availableObjects[i];
        }
        
        return availableObjects[availableObjects.Count - 1]; // Fallback
    }
    
    private Vector2 GetValidSpawnPosition(List<Vector2> usedPositions, SpawnableObject spawnableObj)
    {
        Vector2 randomPoint = GetRandomPointInZone();
        return randomPoint;
    }
    
    private Vector2 GetRandomPointInZone()
    {
        if (zoneColliders.Count == 0) return Vector2.zero;
        
        // Calculate total area of all colliders for weighted selection
        List<float> colliderAreas = new List<float>();
        float totalArea = 0f;
        
        foreach (Collider2D collider in zoneColliders)
        {
            float area = collider.bounds.size.x * collider.bounds.size.y;
            colliderAreas.Add(area);
            totalArea += area;
        }
        
        // Select a collider based on area weighting
        float randomValue = Random.Range(0f, totalArea);
        float currentWeight = 0f;
        int selectedColliderIndex = 0;
        
        for (int i = 0; i < colliderAreas.Count; i++)
        {
            currentWeight += colliderAreas[i];
            if (randomValue <= currentWeight)
            {
                selectedColliderIndex = i;
                break;
            }
        }
        
        // Get random point within the selected collider
        Collider2D selectedCollider = zoneColliders[selectedColliderIndex];
        Bounds bounds = selectedCollider.bounds;
        
        int maxAttempts = 100;
        for (int i = 0; i < maxAttempts; i++)
        {
            Vector2 randomPoint = new Vector2(
                Random.Range(bounds.min.x, bounds.max.x),
                Random.Range(bounds.min.y, bounds.max.y)
            );
            
            if (selectedCollider.OverlapPoint(randomPoint))
                return randomPoint;
        }
        
        // Fallback to collider center
        return selectedCollider.bounds.center;
    }
    
    [ContextMenu("Clear Spawned Objects")]
    public void ClearSpawnedObjects()
    {
        foreach (GameObject obj in spawnedObjects)
        {
            if (obj != null)
            {
                NetworkObject netObj = obj.GetComponent<NetworkObject>();
                if (netObj != null && netObj.IsSpawned)
                {
                    netObj.Despawn(true);
                }
                else
                {
                    DestroyImmediate(obj);
                }
            }
        }
        spawnedObjects.Clear();
        hasSpawned = false;
    }
    
    private void OnDrawGizmos()
    {
        if (!visualizeZone) return;
        
        // Use the serialized zoneColliders list
        foreach (Collider2D col in zoneColliders)
        {
            if (col != null)
            {
                Gizmos.color = new Color(zoneColor.r, zoneColor.g, zoneColor.b, 0.3f);
                Gizmos.DrawCube(col.bounds.center, col.bounds.size);
                
                Gizmos.color = zoneColor;
                Gizmos.DrawWireCube(col.bounds.center, col.bounds.size);
            }
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!visualizeZone) return;
        
        // Draw spawn points for visualization
        Gizmos.color = Color.red;
        foreach (GameObject obj in spawnedObjects)
        {
            if (obj != null)
                Gizmos.DrawWireSphere(obj.transform.position, 0.5f);
        }
    }

    // Public method for external scripts to trigger spawning
    public void TriggerSpawn()
    {
        if (enableRuntimeSpawning)
        {
            SpawnObjects();
        }
    }

    // Method to spawn after a delay
    public void SpawnAfterDelay(float delay)
    {
        if (enableRuntimeSpawning)
        {
            StartCoroutine(DelayedSpawnCustom(delay));
        }
    }

    private IEnumerator DelayedSpawnCustom(float delay)
    {
        yield return new WaitForSeconds(delay);
        SpawnObjects();
    }
}



