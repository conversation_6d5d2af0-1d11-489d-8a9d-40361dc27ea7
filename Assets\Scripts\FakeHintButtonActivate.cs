using UnityEngine;
using TMPro;
using Unity.Netcode;
using RPGM.Gameplay;

public class FakeHintButtonActivate : NetworkBehaviour
{
    [SerializeField] private GameObject fakeHintButton;
    
    private GameObject localPlayer;
    private bool isImposter = false;
    private Vector3 originalPosition;

    private void Start()
    {
        originalPosition = fakeHintButton.transform.position;
        fakeHintButton.transform.position = new Vector3(-1000, -1000, -1000);
    }

    private void Update()
    {
        if(isImposter) return;

        localPlayer = OwnerOfCharacter.FindLocalPlayer();

        if (localPlayer == null || !localPlayer.GetComponent<PlayerSymbol>().isImposter.Value) return;

        fakeHintButton.transform.position = originalPosition;

        isImposter = true;
    }
}
