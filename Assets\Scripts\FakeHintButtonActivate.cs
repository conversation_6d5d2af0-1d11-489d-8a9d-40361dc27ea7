using UnityEngine;
using TMPro;
using Unity.Netcode;

public class FakeHintButtonActivate : NetworkBehaviour
{
    [SerializeField] private GameObject fakeHintButton;
    
    private GameObject localPlayer;
    private bool isImposter = false;
    private Vector3 originalPosition;

    private void Start()
    {
        originalPosition = fakeHintButton.transform.position;
        fakeHintButton.transform.position = new Vector3(-1000, -1000, -1000);
    }

    private void Update()
    {
        if(isImposter) return;
        
        localPlayer = FindLocalPlayer();

        if (localPlayer == null || !localPlayer.GetComponent<PlayerSymbol>().isImposter.Value) return;

        fakeHintButton.transform.position = originalPosition;

        isImposter = true;
    }

    private GameObject FindLocalPlayer()
    {
        var players = GameObject.FindGameObjectsWithTag("Player");
        foreach (var player in players)
        {
            var netObj = player.GetComponent<NetworkObject>();
            if (netObj != null && netObj.IsOwner)
            {
                Debug.LogError("[FakeHintButton] Found local player: " + player.name);
                return player;
            }
        }
        Debug.LogError("[FakeHintButton] Local player not found.");
        return null;
    }
}
