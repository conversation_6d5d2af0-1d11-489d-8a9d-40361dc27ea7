using UnityEngine;
using TMPro;

public class SymbolViewCounter : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI counterText;
    
    private int viewsRemaining = 2;
    private int currentRound = -1; // Track the current round
    
    private void Start()
    {
        UpdateUI();
    }
    
    private void Update()
    {
        // Check if round has changed and reset counter
        if (RoundAndTime.round != currentRound)
        {
            currentRound = RoundAndTime.round;
            ResetCounter();
            
            // Force a new symbol retrieval when the round changes
            PlayerSymbol.RetrieveConversationPlayerSymbol();
        }
    }
    
    // Call this before showing a symbol to check if it's allowed
    public bool CanViewSymbol()
    {
        return viewsRemaining > 0;
    }
    
    // Call this when a symbol is viewed
    public void SymbolViewed()
    {
        if (viewsRemaining > 0)
        {
            viewsRemaining--;
            UpdateUI();
        }
    }
    
    private void UpdateUI()
    {
        if (counterText != null)
        {
            counterText.text = viewsRemaining.ToString();
        }
    }
    
    // Reset the counter (for new rounds)
    public void ResetCounter()
    {
        viewsRemaining = 2;
        UpdateUI();
    }
}


