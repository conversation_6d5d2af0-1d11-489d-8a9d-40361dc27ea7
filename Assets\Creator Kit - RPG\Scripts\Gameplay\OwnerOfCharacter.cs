using UnityEngine;
using Unity.Netcode;

namespace RPGM.Gameplay
{
    public class OwnerOfCharacter : NetworkBehaviour
    {
        public CharacterController2D characterController2D;
        
        public override void OnNetworkSpawn()
        {
            StartCoroutine(CheckOwnershipWithDelay());
        }

        private System.Collections.IEnumerator CheckOwnershipWithDelay()
        {
            // Wait longer and check multiple times for 4+ players
            int maxAttempts = 10;
            float waitTime = 0.1f;
            
            for (int i = 0; i < maxAttempts; i++)
            {
                yield return new WaitForSeconds(waitTime);
                
                if (IsOwner)
                {
                    Debug.LogError($"[OwnerOfCharacter] Ownership confirmed for {gameObject.name} after {i + 1} attempts");
                    
                    // Ensure CharacterController2D is properly enabled
                    if (characterController2D != null)
                    {
                        characterController2D.enabled = true;
                        Debug.LogError($"[OwnerOfCharacter] CharacterController2D enabled for {gameObject.name}");
                    }
                    
                    // Start constant monitoring
                    StartCoroutine(ConstantCharacterControllerCheck());
                    
                    yield break; // Exit early if ownership is confirmed
                }
                
                waitTime += 0.05f; // Increase wait time each attempt
            }
            
            // If we get here, this player is not the owner
            Debug.LogError($"[OwnerOfCharacter] Disabling {gameObject.name} - not owner after {maxAttempts} attempts");
            
            // Disable CharacterController2D for non-owners
            if (characterController2D != null)
            {
                characterController2D.enabled = false;
            }
            enabled = false;
        }

        private System.Collections.IEnumerator ConstantCharacterControllerCheck()
        {
            while (IsOwner && enabled)
            {
                yield return new WaitForSeconds(0.5f);
                
                // Only re-enable if we're the owner and not in conversation
                if (characterController2D != null && !characterController2D.enabled)
                {
                    // Check if we're in conversation (which legitimately disables movement)
                    bool inConversation = FindObjectOfType<RPGM.UI.DialogController>()?.gameObject.activeInHierarchy ?? false;
                    
                    if (!inConversation)
                    {
                        characterController2D.enabled = true;
                        Debug.LogError($"[OwnerOfCharacter] Auto-restored CharacterController2D for {gameObject.name}");
                    }
                }
            }
        }
    }
}



