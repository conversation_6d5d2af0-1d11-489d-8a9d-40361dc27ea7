using UnityEngine;
using TMPro;
using Unity.Netcode;

public class PostItNoteNetworkObject : NetworkBehaviour
{
    public void Despawn()
    {
        Debug.LogError("Despawning Object");
        RequestDespawnServerRpc();
    }

    [ServerRpc(RequireOwnership = false)]
    private void RequestDespawnServerRpc()
    {
        if (gameObject.TryGetComponent(out NetworkObject netObj) && netObj.IsSpawned)
        {
            netObj.Despawn(true);
        }
    }
}
